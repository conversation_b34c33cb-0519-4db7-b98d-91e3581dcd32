# 일반 사용자 관리 시스템

## 개요
학교 관리자(school_admin)가 해당 학교 소속의 일반 사용자(role: 'user')를 관리할 수 있는 시스템입니다.

## 주요 기능

### 1. 사용자 목록 조회
- 학교 관리자는 자신의 학교(school_code)에 속한 일반 사용자만 조회 가능
- 페이지네이션 지원 (페이지당 10명)
- 실시간 검색 기능 (사용자 ID 기준)

### 2. 사용자 CRUD 기능
- **생성**: 새로운 일반 사용자 추가
- **조회**: 사용자 목록 및 상세 정보 확인
- **수정**: 사용자 ID 및 비밀번호 변경
- **삭제**: 사용자 계정 삭제

### 3. 엑셀 업로드 기능
- 여러 사용자를 한 번에 등록 가능
- 지원 형식: .xlsx, .xls, .csv
- 자동 유효성 검사
- 에러 발생 시 상세한 오류 메시지 제공

### 4. 엑셀 템플릿 다운로드
- 표준 엑셀 양식 제공
- 예제 데이터 포함

## 사용 방법

### 접근 권한
- 학교 관리자(role: 'school_admin')만 접근 가능
- 사이드바의 "회원 관리" 메뉴를 통해 접근

### 개별 사용자 추가
1. "사용자 추가" 버튼 클릭
2. 사용자 ID, 비밀번호, 비밀번호 확인 입력
3. "생성" 버튼 클릭

### 엑셀 업로드
1. "템플릿 다운로드" 버튼으로 양식 다운로드
2. 엑셀 파일에 사용자 정보 입력
   - admin_id: 사용자 ID
   - password: 비밀번호
3. "엑셀 업로드" 버튼으로 파일 선택
4. "가져오기" 버튼 클릭

### 사용자 수정
1. 사용자 목록에서 "수정" 버튼 클릭
2. 사용자 ID 변경 또는 새 비밀번호 입력
3. "수정" 버튼 클릭

### 사용자 삭제
1. 사용자 목록에서 "삭제" 버튼 클릭
2. 확인 대화상자에서 "삭제" 버튼 클릭

## 자동 설정 항목
- **role**: 자동으로 'user'로 설정
- **school_code**: 학교 관리자의 school_code로 자동 설정
- **school_nm**: 학교 관리자의 school_nm으로 자동 설정

## 보안 기능
- 학교별 데이터 격리 (다른 학교 사용자 접근 불가)
- 비밀번호 해시화 저장
- 중복 사용자 ID 방지
- 파일 업로드 형식 제한

## 에러 처리
- 유효성 검사 실패 시 상세한 오류 메시지
- 엑셀 업로드 실패 시 행별 오류 정보 제공
- 권한 없는 접근 시 403 에러

## 기술 스택
- Laravel 11
- Livewire 3
- Flux UI
- Maatwebsite/Laravel-Excel
- SQLite Database

## 파일 구조
```
app/
├── Livewire/
│   └── ManageUser.php          # 메인 컴포넌트
├── Imports/
│   └── UsersImport.php         # 엑셀 가져오기
├── Exports/
│   └── UsersTemplateExport.php # 템플릿 내보내기
└── Models/
    └── User.php                # 사용자 모델

resources/views/livewire/
└── manage-user.blade.php       # 뷰 템플릿
```
