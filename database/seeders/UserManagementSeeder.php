<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 학교 관리자 생성
        $schoolAdmin = User::firstOrCreate(
            ['admin_id' => 'school_admin_demo'],
            [
                'password' => Hash::make('password123'),
                'role' => 'school_admin',
                'school_code' => '7010001',
                'school_nm' => '데모초등학교'
            ]
        );

        // 일반 사용자들 생성
        $users = [
            ['admin_id' => 'student001', 'password' => 'password123'],
            ['admin_id' => 'student002', 'password' => 'password456'],
            ['admin_id' => 'student003', 'password' => 'password789'],
            ['admin_id' => 'teacher001', 'password' => 'teacher123'],
            ['admin_id' => 'teacher002', 'password' => 'teacher456'],
        ];

        foreach ($users as $userData) {
            User::firstOrCreate(
                ['admin_id' => $userData['admin_id']],
                [
                    'password' => Hash::make($userData['password']),
                    'role' => 'user',
                    'school_code' => $schoolAdmin->school_code,
                    'school_nm' => $schoolAdmin->school_nm,
                ]
            );
        }

        $this->command->info('사용자 관리 데모 데이터가 생성되었습니다.');
        $this->command->info('학교 관리자: school_admin_demo / password123');
        $this->command->info('일반 사용자: student001~003, teacher001~002 / 각각의 비밀번호');
    }
}
