<?php

use App\Livewire\ManageUser;
use App\Livewire\RequestService;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;
use App\Livewire\DashboardSchoolAdmin;
use App\Livewire\DashboardSchoolUser;
use App\Livewire\QnA;

Route::get('/', function () {
    return view('home');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::get('dashboard-school-admin', DashboardSchoolAdmin::class)
    ->middleware(['auth', 'verified'])
    ->name('dashboard-school-admin');

Route::get('dashboard-school-user', DashboardSchoolUser::class)
    ->middleware(['auth', 'verified'])
    ->name('dashboard_school_user');

Route::get('manage-user', ManageUser::class)
    ->middleware(['auth', 'verified'])
    ->name('manage-user');

Route::get('service/{serviceId}', \App\Livewire\ServicePage::class)
    ->middleware(['auth', 'verified'])
    ->name('service.page');

Route::get('request-service', RequestService::class)
    ->middleware(['auth', 'verified'])
    ->name('request-service');

Route::get('qna', QnA::class)
->middleware(['auth', 'verified'])
->name('qna');

Route::get('qna-admin', App\Livewire\QnaAdmin::class)
    ->middleware(['auth', 'verified'])
    ->name('qna-admin');
    
Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

Route::get('manage-sw-services', App\Livewire\ManageSwServices::class)
    ->middleware(['auth', 'verified'])
    ->name('manage-sw-services');

Route::get('school-admin-services', App\Livewire\SchoolAdminServices::class)
    ->middleware(['auth', 'verified'])
    ->name('school-admin-services');

require __DIR__.'/auth.php';
