<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <flux:sidebar sticky stashable class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />
            <a href="{{ route('dashboard') }}" class="mr-5 flex items-center space-x-2" wire:navigate>
                <x-app-logo />
            </a>

            <flux:navlist variant="outline">
                <flux:navlist.group :heading="__('제공 서비스')" class="grid">
                
                    @if(auth()->user()->role === 'school_admin')
                        <flux:navlist.item icon="home" :href="route('dashboard-school-admin')" :current="request()->routeIs('dashboard-school-admin')" wire:navigate>{{ __('대시보드') }}</flux:navlist.item>
                        <flux:navlist.item icon="cog" :href="route('school-admin-services')" :current="request()->routeIs('school-admin-services')" wire:navigate>{{ __('서비스 관리') }}</flux:navlist.item>
                        <flux:navlist.item icon="users" :href="route('manage-user')" :current="request()->routeIs('manage-user')" wire:navigate>{{ __('회원 관리') }}</flux:navlist.item>
                    
                    @elseif(auth()->user()->role === 'admin')
                        <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>{{ __('대시보드') }}</flux:navlist.item>
                        <flux:navlist.item icon="cog" :href="route('manage-sw-services')" :current="request()->routeIs('manage-sw-services')" wire:navigate>{{ __('서비스 관리') }}</flux:navlist.item>
                        <flux:navlist.item icon="users" :href="route('qna-admin')" :current="request()->routeIs('qna-admin')" wire:navigate>{{ __('Q&A 관리') }}</flux:navlist.item>
                    
                    @elseif(auth()->user()->role === 'user')
                        <flux:navlist.item icon="home" :href="route('dashboard_school_user')" :current="request()->routeIs('dashboard_school_user')" wire:navigate>{{ __('대시보드(user)') }}</flux:navlist.item>
                        <flux:navlist.item icon="users" :href="route('qna')" :current="request()->routeIs('qna')" wire:navigate>{{ __('Q&A') }}</flux:navlist.item>
                        
                        @php
                            $activeServices = \App\Models\ManageService::where('school_code', auth()->user()->school_code)
                                ->where('is_active', true)
                                ->with('swService')
                                ->get();
                        @endphp
                        @if($activeServices->count() > 0)
                            <flux:navlist.group :heading="__('학교 서비스')" class="grid">
                                @foreach($activeServices as $service)
                                    <flux:navlist.item icon="check-circle" href="#" wire:navigate>
                                        {{ $service->swService->service_name }}
                                    </flux:navlist.item>
                                @endforeach
                            </flux:navlist.group>
                        @endif

                    @endif

                </flux:navlist.group>
            </flux:navlist>
            <flux:separator variant="subtle" />
            
            


            <flux:spacer />

            <!-- Desktop User Menu -->
            <flux:dropdown position="bottom" align="start">
                @if (auth()->user()->role=='admin')
                    <flux:profile
                        :name="auth()->user()->admin_id"
                        :initials="auth()->user()->initials()"
                        icon-trailing="chevrons-up-down"
                        />
                @elseif(auth()->user()->role=='school_admin')
                    <flux:profile
                        :name="auth()->user()->admin_id"
                        :initials="auth()->user()->initials()"
                        icon-trailing="chevrons-up-down"
                        />              
                @else
                    <flux:profile
                        :name="auth()->user()->admin_id"
                        :initials="auth()->user()->initials()"
                        icon-trailing="chevrons-up-down"
                        />
                @endif

                <flux:menu class="w-[220px]">
                    
                    <flux:menu.radio.group>
                        
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <!-- <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        {{ auth()->user()->initials() }}
                                        
                                    </span>
                                </span> -->

                                <div class="grid flex-1 text-left text-sm leading-tight">
                                    <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                    </svg>

                                        @if (auth()->user()->role=='admin')
                                            <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                            <span class="truncate text-xs">직책 : {{ auth()->user()->role }}</span>
                                        @elseif(auth()->user()->role=='school_admin')
                                            <span class="truncate font-semibold">{{ auth()->user()->name }}</span>


                                            <span class="truncate text-xs">직책 : {{ auth()->user()->role }}</span>
                                        @else
                                            <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                            <span class="truncate text-xs">직책 :교직원</span>
                                        @endif
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Mobile User Menu -->
        <flux:header class="lg:hidden">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <flux:spacer />

            <flux:dropdown position="top" align="end">
                <flux:profile
                    :initials="auth()->user()->initials()"
                    icon-trailing="chevron-down"
                />

                <flux:menu>
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        {{ auth()->user()->initials() }}
                                        
                                    </span>
                                </span>

                                <div class="grid flex-1 text-left text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        {{ $slot }}

        @fluxScripts
    </body>
</html>
