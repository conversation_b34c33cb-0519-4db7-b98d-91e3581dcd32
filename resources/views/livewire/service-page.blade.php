<div class="p-6">
    <div class="mb-6">
        <flux:heading size="xl">{{ $service->service_name }}</flux:heading>
        <flux:subheading>서비스 ID: {{ $service->id }}</flux:subheading>
    </div>

    <!-- Service Info Card -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6 mb-6">
        <div class="flex items-center mb-4">
            <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">{{ $service->service_name }}</h3>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">활성화된 서비스</p>
            </div>
        </div>

        @if($service->service_detail)
            <div class="mb-4">
                <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">서비스 설명</h4>
                <p class="text-zinc-600 dark:text-zinc-400">{{ $service->service_detail }}</p>
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">서비스 정보</h4>
                <dl class="space-y-1">
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">서비스 ID:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ $service->id }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">서비스명:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ $service->service_name }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">서비스 상태:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $service->service_state ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' }}">
                                {{ $service->service_state ? '활성' : '비활성' }}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>

            <div>
                <h4 class="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">학교 정보</h4>
                <dl class="space-y-1">
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">학교명:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ auth()->user()->school_nm }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">학교 코드:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ auth()->user()->school_code }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-zinc-500 dark:text-zinc-400">사용자 ID:</dt>
                        <dd class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ auth()->user()->admin_id }}</dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>

    <!-- Temporary Notice -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex items-center">
            <svg class="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
                <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">임시 페이지</h4>
                <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    이 페이지는 임시 페이지입니다. 실제 서비스 기능은 추후 구현될 예정입니다.
                </p>
            </div>
        </div>
    </div>
</div>
