<div class="p-6">
    <div class="mb-6">
        <flux:heading size="xl">컴퓨터/프린터 등 IT 기기 수리 요청 게시판</flux:heading>
        <flux:subheading>{{ auth()->user()->school_nm }} 소속 사용자들의 IT 기기 수리 요청을 관리합니다.</flux:subheading>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mb-4 rounded-lg border border-green-200 bg-green-50 p-4 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400">
            <div class="flex items-center">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                {{ session('message') }}
            </div>
        </div>
    @endif

    <!-- Navigation Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button
                wire:click="setActiveTab('request')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'request' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                요청하기
            </button>
            <button
                wire:click="setActiveTab('list')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'list' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                요청리스트
            </button>
            <button
                wire:click="setActiveTab('completed')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'completed' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                확인리스트
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">

        <!-- 요청하기 Tab -->
        @if($activeTab === 'request')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">새 수리 요청</h3>

                <form wire:submit="submitRequest" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">학년</label>
                        <input
                            type="text"
                            wire:model="grade"
                            placeholder="학년 예) 1학년 또는 전담실"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        >
                        @error('grade') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">반</label>
                        <input
                            type="text"
                            wire:model="class"
                            placeholder="반"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        >
                        @error('class') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">요구사항</label>
                        <textarea
                            wire:model="request"
                            placeholder="증상을 자세히 적어주세요."
                            rows="4"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        ></textarea>
                        @error('request') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">진행상태</label>
                        <input
                            type="text"
                            value="요청"
                            readonly
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-zinc-600 text-gray-500 dark:text-gray-400"
                        >
                    </div>

                    <div class="pt-4">
                        <button
                            type="submit"
                            class="w-full sm:w-auto px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            요청하기
                        </button>
                    </div>
                </form>
            </div>
        @endif

        <!-- 요청리스트 Tab -->
        @if($activeTab === 'list')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">수리 요청 목록</h3>

                <div class="w-full overflow-x-auto rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800">
                    <table class="w-full table-auto divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-900">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">학년</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">반</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">요청사항</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-32">진행상태</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-32">완료 확인</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($pendingRequests as $request)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $request->grade }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $request->class }}
                                    </td>
                                    <td class="px-4 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                        <div class="break-words">{{ $request->request }}</div>
                                        <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                            {{ $request->created_at->format('Y-m-d H:i') }} | {{ $request->user->admin_id }}
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <div class="flex flex-col space-y-1">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                                {{ $request->status }}
                                            </span>
                                            <button
                                                wire:click="openCommentModal({{ $request->id }})"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-xs font-medium"
                                            >
                                                댓글달기
                                            </button>
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <button
                                            wire:click="markAsComplete({{ $request->id }})"
                                            class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                        >
                                            처리완료
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="px-6 py-12 text-center">
                                        <div class="text-zinc-500 dark:text-zinc-400">
                                            등록된 수리 요청이 없습니다.
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($pendingRequests->hasPages())
                    <div class="mt-6">
                        {{ $pendingRequests->links() }}
                    </div>
                @endif
            </div>
        @endif

        <!-- 확인리스트 Tab -->
        @if($activeTab === 'completed')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">완료된 수리 요청</h3>

                <div class="w-full overflow-x-auto rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800">
                    <table class="w-full table-auto divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-900">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">학년</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">반</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">요청사항</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-32">진행상태</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($completedRequests as $request)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $request->grade }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $request->class }}
                                    </td>
                                    <td class="px-4 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                        <div class="break-words">{{ $request->request }}</div>
                                        <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                            완료: {{ $request->updated_at->format('Y-m-d H:i') }} | {{ $request->user->admin_id }}
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                            {{ $request->status }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="px-6 py-12 text-center">
                                        <div class="text-zinc-500 dark:text-zinc-400">
                                            완료된 수리 요청이 없습니다.
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($completedRequests->hasPages())
                    <div class="mt-6">
                        {{ $completedRequests->links() }}
                    </div>
                @endif
            </div>
        @endif
    </div>

    <!-- SweetAlert2 스크립트 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // 댓글 모달 열기
        window.addEventListener('open-comment-modal', event => {
            const requestId = event.detail.requestId;
            const comments = event.detail.comments;

            let commentsHtml = '';
            if (comments && comments.length > 0) {
                comments.forEach((comment, index) => {
                    const date = new Date(comment.created_at).toLocaleString('ko-KR');
                    const bgColor = index % 2 === 0 ? '#ffffff' : '#f8fafc';
                    commentsHtml += `
                        <div class="mb-3 p-4 rounded-xl text-left shadow-sm border border-gray-100" style="background-color: ${bgColor};">
                            <div class="flex justify-between items-start mb-2">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold mr-2">
                                        ${comment.user.admin_id.charAt(0).toUpperCase()}
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900">${comment.user.admin_id}</span>
                                </div>
                                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">📅 ${date}</span>
                            </div>
                            <div class="ml-10">
                                <p class="text-sm text-gray-700 leading-relaxed">${comment.comment}</p>
                            </div>
                        </div>
                    `;
                });
            } else {
                commentsHtml = `
                    <div class="text-center py-8">
                        <div class="text-6xl mb-4">💭</div>
                        <p class="text-sm text-gray-500">아직 댓글이 없습니다.</p>
                        <p class="text-xs text-gray-400 mt-1">첫 번째 댓글을 작성해보세요!</p>
                    </div>
                `;
            }

            Swal.fire({
                title: '<span style="color: #1f2937;">💬 댓글</span>',
                html: `
                    <div class="text-left" style="color: #374151;">
                        <div class="mb-4 max-h-60 overflow-y-auto" style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 12px; background-color: #f9fafb;">
                            ${commentsHtml}
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2" style="color: #374151;">✍️ 새 댓글 작성</label>
                            <textarea
                                id="newComment"
                                placeholder="댓글을 입력하세요..."
                                rows="4"
                                style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 14px; resize: vertical; transition: border-color 0.2s;"
                                onfocus="this.style.borderColor='#3b82f6'"
                                onblur="this.style.borderColor='#e5e7eb'"
                            ></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-comment"></i> 댓글 추가',
                cancelButtonText: '<i class="fas fa-times"></i> 닫기',
                confirmButtonColor: '#3b82f6',
                cancelButtonColor: '#6b7280',
                width: '650px',
                padding: '2rem',
                background: '#ffffff',
                customClass: {
                    popup: 'rounded-2xl shadow-2xl',
                    title: 'text-xl font-bold mb-4',
                    confirmButton: 'px-6 py-3 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200',
                    cancelButton: 'px-6 py-3 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200'
                },
                buttonsStyling: true,
                focusConfirm: false,
                preConfirm: () => {
                    const comment = document.getElementById('newComment').value;
                    if (!comment.trim()) {
                        Swal.showValidationMessage('📝 댓글을 입력해주세요.');
                        return false;
                    }
                    if (comment.length > 500) {
                        Swal.showValidationMessage('📏 댓글은 500자 이하로 입력해주세요.');
                        return false;
                    }
                    return comment;
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Livewire 메서드 호출
                    @this.call('addCommentFromModal', requestId, result.value);
                }
            });
        });

        // 댓글 추가 성공 알림
        window.addEventListener('comment-added', event => {
            Swal.fire({
                icon: 'success',
                title: '🎉 댓글 추가 완료!',
                text: '댓글이 성공적으로 추가되었습니다.',
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false,
                toast: true,
                position: 'top-end',
                customClass: {
                    popup: 'rounded-xl shadow-2xl',
                    title: 'text-sm font-semibold',
                    content: 'text-xs'
                },
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            });
        });
    </script>
</div>
