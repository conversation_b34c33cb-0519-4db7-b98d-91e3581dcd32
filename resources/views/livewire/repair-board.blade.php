<div class="p-6">
    <div class="mb-6">
        <flux:heading size="xl">컴퓨터/프린터 등 IT 기기 수리 요청 게시판</flux:heading>
        <flux:subheading>{{ auth()->user()->school_nm }} 소속 사용자들의 IT 기기 수리 요청을 관리합니다.</flux:subheading>
    </div>



    <!-- Navigation Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button
                wire:click="setActiveTab('request')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'request' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                요청하기
            </button>
            <button
                wire:click="setActiveTab('list')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'list' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                요청리스트
            </button>
            <button
                wire:click="setActiveTab('completed')"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'completed' ? 'border-blue-500 text-blue-600 dark:text-blue-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}"
            >
                확인리스트
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 p-6">

        <!-- 요청하기 Tab -->
        @if($activeTab === 'request')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">새 수리 요청</h3>

                <form wire:submit="submitRequest" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">학년</label>
                        <input
                            type="text"
                            wire:model="grade"
                            placeholder="학년 예) 1학년 또는 전담실"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        >
                        @error('grade') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">반</label>
                        <input
                            type="text"
                            wire:model="class"
                            placeholder="반"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        >
                        @error('class') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">요구사항</label>
                        <textarea
                            wire:model="request"
                            placeholder="증상을 자세히 적어주세요."
                            rows="4"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                        ></textarea>
                        @error('request') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">진행상태</label>
                        <input
                            type="text"
                            value="요청"
                            readonly
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-zinc-600 text-gray-500 dark:text-gray-400"
                        >
                    </div>

                    <div class="pt-4">
                        <button
                            type="submit"
                            class="w-full sm:w-auto px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            요청하기
                        </button>
                    </div>
                </form>
            </div>
        @endif

        <!-- 요청리스트 Tab -->
        @if($activeTab === 'list')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">수리 요청 목록</h3>

                <div class="w-full overflow-x-auto rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800">
                    <table class="w-full table-auto divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-900">
                            <tr class="divide-x divide-zinc-200 dark:divide-zinc-700">
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20 bg-gray-100 dark:bg-zinc-800">학년</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">반</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider bg-gray-100 dark:bg-zinc-800">요청사항</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-24">진행상태</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20 bg-gray-100 dark:bg-zinc-800">댓글</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-32">완료 확인</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($pendingRequests as $request)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50 divide-x divide-zinc-200 dark:divide-zinc-700">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100 text-center bg-gray-50 dark:bg-zinc-900/50">
                                        {{ $request->grade }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100 text-center">
                                        {{ $request->class }}
                                    </td>
                                    <td class="px-4 py-4 text-sm text-zinc-900 dark:text-zinc-100 bg-gray-50 dark:bg-zinc-900/50">
                                        <div class="break-words">{{ $request->request }}</div>
                                        <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                            {{ $request->created_at->format('Y-m-d H:i') }} | {{ $request->user->admin_id }}
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                                            {{ $request->status }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-center bg-gray-50 dark:bg-zinc-900/50">
                                        <button
                                            wire:click="openCommentModal({{ $request->id }})"
                                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-xs font-medium px-2 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                                        >
                                            💬 댓글
                                        </button>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-center">
                                        <button
                                            wire:click="markAsComplete({{ $request->id }})"
                                            class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                        >
                                            ✅ 처리완료
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-12 text-center">
                                        <div class="text-zinc-500 dark:text-zinc-400">
                                            등록된 수리 요청이 없습니다.
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($pendingRequests->hasPages())
                    <div class="mt-6">
                        {{ $pendingRequests->links() }}
                    </div>
                @endif
            </div>
        @endif

        <!-- 확인리스트 Tab -->
        @if($activeTab === 'completed')
            <div>
                <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">완료된 수리 요청</h3>

                <div class="w-full overflow-x-auto rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800">
                    <table class="w-full table-auto divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-900">
                            <tr class="divide-x divide-zinc-200 dark:divide-zinc-700">
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20 bg-gray-100 dark:bg-zinc-800">학년</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-20">반</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider bg-gray-100 dark:bg-zinc-800">요청사항</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider w-32">진행상태</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-zinc-800 divide-y divide-zinc-200 dark:divide-zinc-700">
                            @forelse($completedRequests as $request)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/50 divide-x divide-zinc-200 dark:divide-zinc-700">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100 text-center">
                                        {{ $request->grade }}
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-zinc-900 dark:text-zinc-100 text-center">
                                        {{ $request->class }}
                                    </td>
                                    <td class="px-4 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                        <div class="break-words">{{ $request->request }}</div>
                                        <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                                            완료: {{ $request->updated_at->format('Y-m-d H:i') }} | {{ $request->user->admin_id }}
                                        </div>
                                    </td>
                                    <td class="px-4 py-4 whitespace-nowrap text-center">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                                            ✅ {{ $request->status }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="px-6 py-12 text-center">
                                        <div class="text-zinc-500 dark:text-zinc-400">
                                            완료된 수리 요청이 없습니다.
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                @if($completedRequests->hasPages())
                    <div class="mt-6">
                        {{ $completedRequests->links() }}
                    </div>
                @endif
            </div>
        @endif
    </div>

    <!-- 댓글 모달 -->
    @if($showCommentModal)
        <div class="fixed inset-0 z-50 flex items-center justify-center p-4" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="fixed inset-0 bg-black bg-opacity-60 transition-opacity" wire:click="closeCommentModal"></div>

            <div class="relative bg-white dark:bg-zinc-900 rounded-xl shadow-2xl transform transition-all w-full max-w-md border border-zinc-200 dark:border-zinc-600 max-h-[80vh] overflow-hidden">
                <div class="bg-white dark:bg-zinc-900 p-4">
                    <div class="mb-3">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                            💬 댓글
                        </h3>
                    </div>

                    <!-- 기존 댓글 목록 -->
                    <div class="mb-3 max-h-40 overflow-y-auto bg-gray-50 dark:bg-zinc-800 rounded-lg p-3 border border-gray-200 dark:border-zinc-700">
                        @if(session()->has('comment_message'))
                            <div class="mb-2 p-2 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded text-sm">
                                {{ session('comment_message') }}
                            </div>
                        @endif

                        @forelse($requestComments as $comment)
                            <div class="mb-2 p-2 bg-white dark:bg-zinc-700 rounded-md shadow-sm border border-gray-100 dark:border-zinc-600">
                                <div class="flex justify-between items-start mb-1">
                                    <div class="flex items-center">
                                        <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold mr-2">
                                            {{ strtoupper(substr($comment['user']['admin_id'], 0, 1)) }}
                                        </div>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $comment['user']['admin_id'] }}
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-zinc-600 px-2 py-1 rounded-full">
                                        {{ \Carbon\Carbon::parse($comment['created_at'])->format('m/d H:i') }}
                                    </span>
                                </div>
                                <p class="text-sm text-gray-700 dark:text-gray-300 ml-8 leading-relaxed">{{ $comment['comment'] }}</p>
                            </div>
                        @empty
                            <div class="text-center py-6">
                                <div class="text-3xl mb-2">💭</div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">아직 댓글이 없습니다.</p>
                                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">첫 번째 댓글을 작성해보세요!</p>
                            </div>
                        @endforelse
                    </div>

                    <!-- 새 댓글 작성 -->
                    <form wire:submit="addComment">
                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">✍️ 새 댓글 작성</label>
                            <textarea
                                wire:model="newComment"
                                placeholder="댓글을 입력하세요..."
                                rows="2"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white resize-none"
                            ></textarea>
                            @error('newComment') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                        </div>

                        <div class="flex justify-end space-x-2 pt-2 border-t border-gray-200 dark:border-zinc-700">
                            <button
                                type="button"
                                wire:click="closeCommentModal"
                                class="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-600 hover:bg-gray-50 dark:hover:bg-zinc-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
                            >
                                ❌ 닫기
                            </button>
                            <button
                                type="submit"
                                class="px-3 py-1.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                            >
                                💬 댓글 추가
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <!-- SweetAlert2 Scripts -->
    <script>
        // 수리 요청 성공 알림
        window.addEventListener('request-submitted', event => {
            Swal.fire({
                icon: 'success',
                title: '🎉 요청 완료!',
                text: '수리 요청이 성공적으로 등록되었습니다.',
                confirmButtonText: '확인',
                confirmButtonColor: '#3b82f6',
                timer: 3000,
                timerProgressBar: true,
                customClass: {
                    popup: 'rounded-xl shadow-2xl',
                    title: 'text-lg font-semibold',
                    content: 'text-sm',
                    confirmButton: 'px-6 py-2 rounded-lg font-medium'
                },
                didOpen: (popup) => {
                    popup.addEventListener('mouseenter', Swal.stopTimer)
                    popup.addEventListener('mouseleave', Swal.resumeTimer)
                }
            });
        });
    </script>
</div>
