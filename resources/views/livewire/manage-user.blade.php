<div class="p-6">
    <div class="mb-6">
        <flux:heading size="xl">일반 사용자 관리</flux:heading>
        <flux:subheading>{{ auth()->user()->school_nm }} 소속 일반 사용자를 관리합니다.</flux:subheading>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <flux:banner variant="success" class="mb-4">
            {{ session('message') }}
        </flux:banner>
    @endif

    @if (session()->has('error'))
        <flux:banner variant="danger" class="mb-4">
            {{ session('error') }}
        </flux:banner>
    @endif

    <!-- Statistics Cards -->
    <div class="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div class="rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <flux:icon.users class="h-8 w-8 text-blue-600" />
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-zinc-500 dark:text-zinc-400">총 사용자</div>
                    <div class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">{{ $users->total() }}</div>
                </div>
            </div>
        </div>

        <div class="rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <flux:icon.academic-cap class="h-8 w-8 text-green-600" />
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-zinc-500 dark:text-zinc-400">학교명</div>
                    <div class="text-lg font-bold text-zinc-900 dark:text-zinc-100">{{ auth()->user()->school_nm }}</div>
                </div>
            </div>
        </div>

        <div class="rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-800">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <flux:icon.identification class="h-8 w-8 text-purple-600" />
                </div>
                <div class="ml-4">
                    <div class="text-sm font-medium text-zinc-500 dark:text-zinc-400">학교 코드</div>
                    <div class="text-lg font-bold text-zinc-900 dark:text-zinc-100">{{ auth()->user()->school_code }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Bar -->
    <div class="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center">
            <!-- Search -->
            <flux:input
                wire:model.live="search"
                placeholder="사용자 ID로 검색..."
                icon="magnifying-glass"
                class="w-full sm:w-64"
            />
        </div>

        <div class="flex flex-col gap-2 sm:flex-row">
            <!-- Excel Upload -->
            <div class="flex items-center gap-2">
                <flux:input
                    type="file"
                    wire:model="excelFile"
                    accept=".xlsx,.xls,.csv"
                    class="hidden"
                    id="excel-upload"
                />
                <flux:button
                    variant="outline"
                    size="sm"
                    onclick="document.getElementById('excel-upload').click()"
                >
                    엑셀 업로드
                </flux:button>

                @if($excelFile)
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-zinc-600 dark:text-zinc-400">
                            {{ $excelFile->getClientOriginalName() }}
                        </span>
                        <flux:button
                            wire:click="importUsers"
                            variant="primary"
                            size="sm"
                            wire:loading.attr="disabled"
                            wire:target="importUsers"
                        >
                            <span wire:loading.remove wire:target="importUsers">가져오기</span>
                            <span wire:loading wire:target="importUsers">처리중...</span>
                        </flux:button>
                    </div>
                @endif
            </div>

            <!-- Template Download -->
            <flux:button
                wire:click="downloadTemplate"
                variant="outline"
                size="sm"
                icon="arrow-down-tray"
            >
                템플릿 다운로드
            </flux:button>

            <!-- Create User -->
            <flux:button
                wire:click="openCreateModal"
                variant="primary"
                size="sm"
                icon="plus"
            >
                사용자 추가
            </flux:button>
        </div>
    </div>

    <!-- Users Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-700">
        <flux:table>
            <flux:columns>
                <flux:column>ID</flux:column>
                <flux:column>사용자 ID</flux:column>
                <flux:column>학교명</flux:column>
                <flux:column>생성일</flux:column>
                <flux:column>작업</flux:column>
            </flux:columns>

            <flux:rows>
                @forelse($users as $user)
                    <flux:row>
                        <flux:cell>{{ $user->id }}</flux:cell>
                        <flux:cell>{{ $user->admin_id }}</flux:cell>
                        <flux:cell>{{ $user->school_nm }}</flux:cell>
                        <flux:cell>{{ $user->created_at->format('Y-m-d H:i') }}</flux:cell>
                        <flux:cell>
                            <div class="flex gap-2">
                                <flux:button
                                    wire:click="openEditModal({{ $user->id }})"
                                    variant="outline"
                                    size="xs"
                                    icon="pencil"
                                >
                                    수정
                                </flux:button>
                                <flux:button
                                    wire:click="openDeleteModal({{ $user->id }})"
                                    variant="danger"
                                    size="xs"
                                    icon="trash"
                                >
                                    삭제
                                </flux:button>
                            </div>
                        </flux:cell>
                    </flux:row>
                @empty
                    <flux:row>
                        <flux:cell colspan="5" class="text-center py-8">
                            <div class="text-zinc-500 dark:text-zinc-400">
                                등록된 사용자가 없습니다.
                            </div>
                        </flux:cell>
                    </flux:row>
                @endforelse
            </flux:rows>
        </flux:table>
    </div>

    <!-- Pagination -->
    @if($users->hasPages())
        <div class="mt-6">
            {{ $users->links() }}
        </div>
    @endif

    <!-- Create User Modal -->
    <flux:modal name="create-user" :show="$showCreateModal" class="max-w-md">
        <form wire:submit="createUser">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">사용자 추가</flux:heading>
                </div>

                <div class="space-y-4">
                    <flux:field>
                        <flux:label>사용자 ID</flux:label>
                        <flux:input wire:model="admin_id" placeholder="사용자 ID를 입력하세요" />
                        <flux:error name="admin_id" />
                    </flux:field>

                    <flux:field>
                        <flux:label>비밀번호</flux:label>
                        <flux:input type="password" wire:model="password" placeholder="비밀번호를 입력하세요" />
                        <flux:error name="password" />
                    </flux:field>

                    <flux:field>
                        <flux:label>비밀번호 확인</flux:label>
                        <flux:input type="password" wire:model="password_confirmation" placeholder="비밀번호를 다시 입력하세요" />
                        <flux:error name="password_confirmation" />
                    </flux:field>
                </div>

                <div class="flex gap-2 justify-end">
                    <flux:button type="button" variant="ghost" wire:click="closeCreateModal">취소</flux:button>
                    <flux:button type="submit" variant="primary">생성</flux:button>
                </div>
            </div>
        </form>
    </flux:modal>

    <!-- Edit User Modal -->
    <flux:modal name="edit-user" :show="$showEditModal" class="max-w-md">
        <form wire:submit="updateUser">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">사용자 수정</flux:heading>
                </div>

                <div class="space-y-4">
                    <flux:field>
                        <flux:label>사용자 ID</flux:label>
                        <flux:input wire:model="admin_id" placeholder="사용자 ID를 입력하세요" />
                        <flux:error name="admin_id" />
                    </flux:field>

                    <flux:field>
                        <flux:label>새 비밀번호 (변경하지 않으려면 비워두세요)</flux:label>
                        <flux:input type="password" wire:model="password" placeholder="새 비밀번호를 입력하세요" />
                        <flux:error name="password" />
                    </flux:field>

                    <flux:field>
                        <flux:label>비밀번호 확인</flux:label>
                        <flux:input type="password" wire:model="password_confirmation" placeholder="비밀번호를 다시 입력하세요" />
                        <flux:error name="password_confirmation" />
                    </flux:field>
                </div>

                <div class="flex gap-2 justify-end">
                    <flux:button type="button" variant="ghost" wire:click="closeEditModal">취소</flux:button>
                    <flux:button type="submit" variant="primary">수정</flux:button>
                </div>
            </div>
        </form>
    </flux:modal>

    <!-- Delete User Modal -->
    <flux:modal name="delete-user" :show="$showDeleteModal" class="max-w-md">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">사용자 삭제</flux:heading>
                <flux:subheading>정말로 이 사용자를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.</flux:subheading>
            </div>

            <div class="flex gap-2 justify-end">
                <flux:button type="button" variant="ghost" wire:click="closeDeleteModal">취소</flux:button>
                <flux:button wire:click="deleteUser" variant="danger">삭제</flux:button>
            </div>
        </div>
    </flux:modal>
</div>
