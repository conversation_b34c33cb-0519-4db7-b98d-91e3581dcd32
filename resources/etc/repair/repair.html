{% extends 'repair_layout.html' %}
{% block content %}
 


 
   <br>
    <br>
    <br>
    <br>
 <div class="main-container mt-5" id="container">

      <div class="overlay"></div>
      <div class="cs-overlay"></div>
      <div class="search-overlay"></div>




  <div class="container col-xl-12 col-md-12 col-sm-12 col-12">

      <div class="row ">
        <div class="col-lg-12 col-12 layout-spacing">
              <div class="statbox widget box box-shadow">
                  <div class="widget-header header navbar navbar-expand-sm expand-header">                                
                      <div class="row ">
                        
                          <div class="container col-xl-12 col-md-12 col-sm-12 col-12">
                            <ul class="navbar-item theme-brand flex-row">
                              <li class="nav-item">
                                <h4>공지사항</h4>
                              </li>
                              <li>
                                <span class="badge badge-light-primary mt-3" data-bs-toggle="modal" data-bs-target="#inputFormModal">공지쓰기</span> 
                                <div id="modalCustom" class="col-lg-12 layout-spacing">
                                  <div class="statbox widget box box-shadow">
                                          <!-- Modal -->
                                          <div class="modal fade inputForm-modal" id="inputFormModal" tabindex="-1" role="dialog" aria-labelledby="inputFormModalLabel" aria-hidden="true">
                                              <div class="modal-dialog modal-dialog-centered" role="document">
                                                <div class="modal-content">
        
                                                  <div class="modal-header" id="inputFormModalLabel">
                                                      <h5 class="modal-title">공지쓰기</b></h5>
                                                      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
                                                  </div>
                                                  <div class="modal-body">
                                                      <form class="mt-0" method="post" action="../repair_notice_send">
                                                          <div class="form-group">
                                                              <div class="input-group mb-3">
                                                                  <span class="input-group-text">
                                                                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-mail" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                                          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                                          <rect x="3" y="5" width="18" height="14" rx="2"></rect>
                                                                          <polyline points="3 7 12 13 21 7"></polyline>
                                                                      </svg>
                                                                  </span>
                                                                  <input type="text" class="form-control" id="service_id" name="service_id" aria-label="service_id" value="{{ service_id }}" hidden>
                                                                  <input type="text" class="form-control" id="comments_id" name="comments_id" aria-label="comments_id" value="" hidden>
                                                                  <input type="text" class="form-control" id="service_name" name="service_name" aria-label="service_name" value="{{ service_name }}" hidden>
                                                                  <input type="text" class="form-control" id="notice" name="notice" placeholder="공지쓰기" aria-label="공지쓰기">
                                                              </div>
                                                          </div>
                                                          <button type="button" data-bs-dismiss="modal" class="btn btn-light-danger mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">취소</button>
                                                          <button type="submit" class="btn btn-primary mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">공지쓰기</button>
                                                
                                      
                                                      </form>
        
                                                  </div>
                                                

                                              
                                                </div>
                                              </div>
                                          </div>
                                      </div>
                                  </div>

                          
                                </div>
                              </li>
                          </div>
                      </div>
                  </div>
                  <div class="widget-content widget-content-area">

                  <div class="table-responsive table-wrap ">
                     <table class="table table-bordered table-hover table-checkable" id="notice_table">
                          <thead>
                              <tr>
                                  <th>공지사항</th>
                                  
                                  <th>삭제</th>
                              </tr>
                          </thead>
                          <tbody>
                           
                              {% for notice in notices %}
                              <tr>
                                  <td>{{ notice.notice }}</td>
                                  
                                  <td><a href="{{ doc_root }}repair_notice_delete/{{ notice.id }}/{{ service_name }}/{{ service_id }}" class="badge badge-light-danger mt-3">삭제</a></td>
                              </tr>
                             
                              
                              {% endfor %}
                          </tbody>
                      </table>
                        
                  </div>
                  
              </div>
            </div>
          </div>

          <div class="col-lg-12 col-12 layout-spacing">
              <div class="statbox widget box box-shadow">
                  <div class="widget-header">                                
                      <div class="row">
                          <div class="col-xl-12 col-md-12 col-sm-12 col-12 mt-3">
                              <h4>컴퓨터/프린터 수리 요청 게시판</h4>
                          </div>
                      </div>
                  </div>
                  <div class="widget-content widget-content-area">
                    
                      <form name="RequestSend" method="post" action="{{ doc_root }}{{ service_name }}/{{ service_id }}">
                          <div class="form-group mb-4">
                              <label for="exampleFormControlInput2">학년</label>
                              <input type="text" class="form-control"  name="grade" placeholder="학년 예) 1학년 또는 전담실"   required>
                              <div class="errors">{{ errors.grade }}</div>
                          </div>
                          <div class="form-group mb-4">
                              <label for="exampleFormControlSelect1">반</label>
                              <input type="text" class="form-control" name="class" placeholder="반" >
                              <div class="errors">{{ errors.class }}</div>
                              </input>
                          </div>
                          <div class="form-group mb-4">
                              <label for="exampleFormControlSelect2">요구사항</label>
                              <input type="text" class="form-control"  name="request" placeholder="증상을 자세히 적어주세요." required>  
                              <div class="errors">{{ errors.request }}</div>
                              </input>
                          </div>
                          <div class="form-group mb-4">
                              <label for="exampleFormControlSelect1">진행상태</label>
                              <input type="text" class="form-control" id="status"  name="status" value="요청" readonly>
                                  
                              </input>
                          </div>
                      
                          <input type="submit"  name="want_request" class="mt-4 mb-4 btn btn-primary" value="요청하기">
                      </form>
                  </div>
              </div>
          </div>

      </div>
  </div>
</div>     

<script>
  //document.location.reload();
  object.oninvalid = function(){ alert('학년을 입력해주세요'); }
</script>

  
    
{% endblock %}