<?php
declare(strict_types = 1);

use function PHPSTORM_META\type;

$want                   = [];
$data                   = [];
$want['status']         = '';
$complete['status']     = "완료";
$want['status']         = '';
$data['repair_result']  = 'repair_result';
$servcie_name          = '';
$data['comments'][]       = [];


// Use strict types
if (!$id) {                                              // If no valid id
    include APP_ROOT . '/src/pages/page-not-found.php';  // Page not found
}


$data['select_url']    = explode('/',$_SERVER["REQUEST_URI"]);



$data['service_id']  = (int)$data['select_url'][3];
//$data['selected_id'] = (int)$data['select_url'][4];

$service_id = $data['service_id'];
$data['results']      = $cms->getRepair()->getWantsAll('repair', $data['service_id'], '요청');

intval($service_id);
$school_name = $cms->getSchool()->getSchoolName($service_id);
$data['school_name'] = $school_name['SCHUL_NM'];
$data['service_name'] = substr($data['select_url'][2], 0, -8);
$service_name = substr($data['select_url'][2], 0, -8);
strval($service_name);
// for($i = 0; $i < count($data['results']); $i++){
//   $comments_id = $data['results'][$i]['id'];
//   $data['comments'] .= strval($cms->getRepair()->getComments($service_name, $service_id, $comments_id));

// }

$data['comments'] = $cms->getRepair()->getComments($service_name, $service_id);

echo $twig->render('repair_request.html', $data);