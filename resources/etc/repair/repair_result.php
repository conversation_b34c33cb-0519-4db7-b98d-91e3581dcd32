<?php
declare(strict_types = 1);



$want                   = [];
$data                   = [];
$want['status']         = '';
$complete['status']     = "완료";
$refresh_prevent        = '';
$data['repair_result']  = 'repair_result';
$data['repair_request'] = 'repair_request';

// Use strict types
if (!$id) {                                              // If no valid id
    include APP_ROOT . '/src/pages/page-not-found.php';  // Page not found
}


$data['select_url']    = explode('/',$_SERVER["REQUEST_URI"]);


$data['service_name']= (string)$data['select_url'][2];
$data['service_id']  = (int)$data['select_url'][3];
//$data['selected_id'] = (int)$data['select_url'][4];
$school_id = $data['service_id'];



intval($school_id);



$school_name = $cms->getSchool()->getSchoolName($school_id);
$data['school_name'] = $school_name['SCHUL_NM'];
$data['completes']    = $cms->getRepair()->getCompleteLimit('repair', $data['service_id']);



echo $twig->render('repair_result.html', $data);