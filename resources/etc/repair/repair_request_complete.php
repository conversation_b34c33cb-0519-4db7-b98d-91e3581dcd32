<?php
declare(strict_types = 1);                               // Use strict types                           // Import Validate class

                                         // Initialize: from
                                           // Message

//학교 검색 관련 변수
$schoolSearch  = '';
$SCHUL_NM      = '';
$SD_SCHUL_CODE      = '';
$service_name  = 'repair';
$data['service_name'] = $service_name;
                       
$errors        = [];                                          
$success       = $_GET['success'] ?? null;



//school search
$schoolSearch  = $_POST['schoolSearch'];          
$SCHUL_NM      = $_POST['SCHUL_NM'];
$SD_SCHUL_CODE = $_POST['SD_SCHUL_CODE'];



$data['$SD_SCHUL_CODE'] = $SD_SCHUL_CODE;;





if ($_SERVER['REQUEST_METHOD'] == 'POST') {


  $comments_table_name = $service_name . $SD_SCHUL_CODE . "comments";

  $table_name = $service_name . $SD_SCHUL_CODE;

  $q["one"] = "CREATE TABLE $table_name (
                      id INT(255) NOT NULL AUTO_INCREMENT,
                      grade VARCHAR(15) NOT NULL,
                      class VARCHAR(15) NULL,
                      request TEXT NOT NULL,
                      status VARCHAR(15) NOT NULL,
                      PRIMARY KEY (id))";
  $q["two"] = "INSERT INTO repair (SCHUL_NM, SD_SCHUL_CODE, service_name)
                        VALUES ('$SCHUL_NM', '$SD_SCHUL_CODE', '$service_name')";
  $q["three"] = "CREATE TABLE $comments_table_name (
                    id INT(255) NOT NULL AUTO_INCREMENT,
                    service_name VARCHAR(255),
                    service_id INT(255),
                    comments_id INT(255),
                    comments Text(1000),
                    notice Text(1000),
                    level VARCHAR(15) default '0',
                    PRIMARY KEY (id));"; 



  if (!$link = mysqli_connect("localhost", "neohum774", "min9610012@", "neohum774")) {
    echo "Failed to connect to MySQL: ", mysqli_connect_error();
  } elseif (mysqli_multi_query($link, implode(';', $q))) {
    do {
      $q_key = key($q);                                 // current query's key name (Orders or Inventory)
      if ($result = mysqli_store_result($link)) {       // if a result set... SELECTs do
        while ($row = mysqli_fetch_assoc($result)) {  // if one or more rows, iterate all
          $rows[$q_key][] = $row;
        }
        mysqli_free_result($result);
        echo "<div><pre>" . var_export($rows[$q_key], true) . "</pre></div>";
      }
    } while (next($q) && mysqli_more_results($link) && mysqli_next_result($link));
  }
  if ($mysqli_error = mysqli_error($link)) {                // check & declare variable in same step to avoid duplicate func call
    echo "<div style=\"color:red;\">Query Key = ", key($q), ", Query = ", current($q), ", Syntax Error = $mysqli_error</div>";
  } 

}





                          
$data['SCHUL_NM']       = $SCHUL_NM;                          
$data['SD_SCHUL_CODE']  = $SD_SCHUL_CODE;   



echo $twig->render('repair_edit.html', $data);               // Redirect to their page                                                 // Stop code running            