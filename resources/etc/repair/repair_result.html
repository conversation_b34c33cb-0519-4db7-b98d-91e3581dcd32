{% extends 'repair_layout.html' %}
{% block content %}
<br>
<br>
<br>
  <div class="main-container mt-5" id="container">

      <div class="overlay"></div>
      <div class="cs-overlay"></div>
      <div class="search-overlay"></div>

      
  <div class="container col-xl-12 col-md-12 col-sm-12 col-12">

      <div class="row ">

          <div class="col-lg-12 col-12 layout-spacing">
              <div class="statbox widget box box-shadow">
                  <div class="widget-header">                                
                      <div class="row">
                          <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                              <h4>완료 리스트</h4>
                              <!--search-->
                              <div class="container">
     
                          </div>
                      </div>
                  
                  

                      <div class="table-responsive">
                          <table class="table table-bordered">
                              <thead>
                                  <tr>
                                      <th scope="col">학년</th>
                                      <th scope="col">반</th>
                                      <th class="text-center" scope="col">요청사항</th>
                                      <th class="text-center" scope="col">진행상태</th>
                                     
                                  </tr>
                              </thead>
                              <tbody>
                                {% for complete in completes %}
                                  <tr>
                                      <td>
                                          <div class="media">
                                              
                                              <div class="media-body align-self-center">
                                                  <h6 class="mb-0">{{ complete.grade }}</h6>
                                                  
                                              </div>
                                          </div>
                                      </td>
                                      <td>
                                          <p class="mb-0">{{ complete.class }}</p>
                                         
                                      </td>
                                      <td class="text-center">
                                          <p class="mb-0">{{ complete.request }}</p>
                                          
                                      </td>
                                      <td class="text-center">
                                          <span class="badge badge-light-danger">{{ complete.status }}</span> 
                                          
                                      </td>
                                     
                                      
                                  </tr>
                                  
                                {% endfor %}
                              </tbody>
                          </table>
                    
                      </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>  
  </div>


<!--신청 완료 리스트-->

<!--<script>location.replace('{{ doc_root }}{{ service_name }}/{{ service_id }}');</script>
-->

<script>
  //document.location.reload();
  object.oninvalid = function(){ alert('학년을 입력해주세요'); }
</script>

  
    
{% endblock %}