{% extends 'repair_layout.html' %}
{% block content %}
   <br>
    <br>
    <br>
    <br>
 <div class="main-container mt-5" id="container">

      <div class="overlay"></div>
      <div class="cs-overlay"></div>
      <div class="search-overlay"></div>

      
  <div class="container col-xl-12 col-md-12 col-sm-12 col-12">
 
      <div class="row">
          

          <div class="col-lg-12 col-12 layout-spacing">
              <div class="statbox widget box box-shadow">
                  <div class="widget-header">                                
                      <div class="row">
                          <div class="col-xl-12 col-md-12 col-sm-12 col-12">
                              <h4>요청 리스트</h4>
                          </div>
                      </div>
                  </div>
                  <div class="widget-content widget-content-area">

                  <div class="table-responsive table-wrap ">
                    
                      <table class="table table-bordered table--vertical">
                          <thead>
                              <tr>
                                  <th scope="col">학년</th>
                                  <th scope="col">반</th>
                                  <th class="text-center" scope="col">요청사항</th>
                                  <th class="text-center" scope="col">진행상태</th>
                                  <th class="text-center" scope="col">완료 확인</th>
                                  
                              </tr>
                          </thead>
                          <tbody>
                            {% for result in results %}
                              <tr>
                                  <td>
                                      <div class="media">
                                          
                                          <div class="media-body align-self-center">
                                              <h6 class="mb-0">{{ result.grade }}</h6>
                                              
                                          </div>
                                      </div>
                                  </td>
                                  <td>
                                      <p class="mb-0">{{ result.class }}</p>
                                      
                                  </td>
                                  <td class="text-center">
                                      <p class="mb-0">{{ result.request }}</p>
                                      
                                  </td>
                                  <td class="text-center">
                                      <span class="badge badge-light-success">{{ result.status }}</span> 
                                      <span class="badge badge-light-primary" data-bs-toggle="modal" data-bs-target="#inputFormModal{{ result.id }}">댓글 달기</span> 
                                      <br>
                                {% for comment in comments   %} 
                                  
                                  {% if comment.comments_id == result.id %}
                                  {{ comment.comments }}<br>
                                  {% endif %}

                                  {% endfor %}
                                  
                                  
                                
                                    
                                    
                                  
                      <div class="row">

                        <div id="modalCustom" class="col-lg-12 layout-spacing">
                            <div class="statbox widget box box-shadow">
                                    <!-- Modal -->
                                    <div class="modal fade inputForm-modal" id="inputFormModal{{ result.id }}" tabindex="-1" role="dialog" aria-labelledby="inputFormModalLabel" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                          <div class="modal-content">
  
                                            <div class="modal-header" id="inputFormModalLabel">
                                                <h5 class="modal-title">댓글 달기</b></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"><svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
                                            </div>
                                            <div class="modal-body">
                                                <form class="mt-0" method="post" action="{{ doc_root }}repair_comments/{{ service_id }}/{{ result.id }}">
                                                    <div class="form-group">
                                                        <div class="input-group mb-3">
                                                            <span class="input-group-text">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-mail" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                                    <rect x="3" y="5" width="18" height="14" rx="2"></rect>
                                                                    <polyline points="3 7 12 13 21 7"></polyline>
                                                                </svg>
                                                            </span>
                                                            <input type="text" class="form-control" id="service_id" name="service_id" aria-label="service_id" value="{{ service_id }}" hidden>
                                                            <input type="text" class="form-control" id="comments_id" name="comments_id" aria-label="comments_id" value="{{ result.id }}" hidden>
                                                            <input type="text" class="form-control" id="service_name" name="service_name" aria-label="service_name" value="{{ service_name }}" hidden>
                                                            <input type="text" class="form-control" id="comments" name="comments" placeholder="댓글달기" aria-label="comment">
                                                        </div>
                                                    </div>
                                                    <button type="button" data-bs-dismiss="modal" class="btn btn-light-danger mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">취소</button>
                                                    <button type="submit" class="btn btn-primary mt-2 mb-2 btn-no-effect" data-bs-dismiss="modal">댓글 달기</button>
                                          
                                
                                                </form>
  
                                            </div>
                                          

                                        
                                          </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                  </td>
                        
                                    
                                  <td class="text-center">
                                  
                                    <a href="{{ doc_root }}repair_edit/{{ service_name }}/{{ service_id }}/{{ result.id}}" class="badge badge-light-danger">
                                      처리 완료(클릭)
                                    </a>
                                  </td>
                              </tr>
            
                              {% endfor %}
                            
                          </tbody>
                      </table>
                    
                  </div>
                  
              </div>
            </div>
          </div>

      
        </div>
      </div>
    </div>  
  </div>



<!--신청 완료 리스트-->

<!--<script>location.replace('{{ doc_root }}{{ service_name }}/{{ service_id }}');</script>
-->

<script>
  //document.location.reload();
  object.oninvalid = function(){ alert('학년을 입력해주세요'); }
</script>

  
    
{% endblock %}