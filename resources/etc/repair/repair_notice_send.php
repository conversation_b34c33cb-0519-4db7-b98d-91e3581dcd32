<?php 
declare(strict_types = 1);                                           // Use strict types

if ($_SERVER['REQUEST_METHOD'] == 'POST') {                           // If form has been submitted
    $data['result'] = $cms->getRepair()->setNotice($_POST['service_id'], $_POST['service_name'], $_POST['notice'], 1);
    $data['service_id'] = $_POST['service_id'];
    $data['service_name'] = $_POST['service_name'];
    redirect('repair/' . $data['service_id'], $data); // Redirect to correct URL
}