<?php 
declare(strict_types = 1);                                           // Use strict types


$data['select_url']    = explode('/',$_SERVER["REQUEST_URI"]);

$data['id'] = (int)$data['select_url'][3];
$data['service_name']= (string)$data['select_url'][4];
$data['service_id']  = (int)$data['select_url'][5];
//$data['selected_id'] = (int)$data['select_url'][4];
$id = $data['id'];
$service_name = $data['service_name'];
$service_id   = $data['service_id'];


$data['result'] = $cms->getRepair()->deleteNotice($service_name, $service_id, $id);
redirect("repair/" . $service_id, $data); // Redirect to correct URL
