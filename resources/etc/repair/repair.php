<?php
declare(strict_types = 1);



$want                   = [];
$data                   = [];
$want['status']         = '';
$complete['status']     = "완료";
$refresh_prevent        = '';
$data['repair_result']  = 'repair_result';
$data['repair_request'] = 'repair_request';
$school_name            = '';
$_POST['notice']        = '';



$data['select_url']    = explode('/',$_SERVER["REQUEST_URI"]);


$data['service_name']= (string)$data['select_url'][2];
$data['service_id']  = (int)$data['select_url'][3];
//$data['selected_id'] = (int)$data['select_url'][4];
$service_name = $data['service_name'];
$service_id   = $data['service_id'];

$data['notices'] = $cms->getRepair()->getNotice($service_name, $service_id);


if ($_POST['notice'] !== '') {
  
}else {
  if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $want['grade'] = $_POST['grade'];
    $want['class'] = $_POST['class'];
    $want['request'] = $_POST['request'];
    $want['status'] = "요청";
    $data['want'] = $cms->getRepair()->setWant($data['service_name'], $id, $want['grade'], $want['class'], $want['request'], $want['status']);
    redirect("repair_request/" . $data['service_id']); // Redirect to correct URL

}
}




$school_name = $cms->getSchool()->getSchoolName($service_id);

$data['school_name'] = $school_name['SCHUL_NM'];

echo $twig->render('repair.html', $data);