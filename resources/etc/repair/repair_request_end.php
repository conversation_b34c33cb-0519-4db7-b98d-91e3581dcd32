<?php
declare(strict_types = 1);                                           // Use strict types
//$data['articles']    = $cms->getArticle()->getAll(true, null, null, 6); // Get latest article summaries
$data = [];//$data['navigation']  = $cms->getCategory()->getAll();         // Get categories
if ($cms->getSession()->id) {             // If user is already logged in
  $data['id'] = $cms->getSession()->id;                                               // Stop code running
}
$data['SCHEUL_NM'] = $_POST['SCHUL_NM'];
$data['SD_SCHUL_CODE'] = $_POST['SD_SCHUL_CODE'];
$data['service_name'] = $_POST['service_name'];

$sql = $cms->getrepair()->insert($SCHUL_NM, $SD_SCHUL_CODE, $service_name);

echo $twig->render('repair_request_end.html', $data);                             // Render Twig template