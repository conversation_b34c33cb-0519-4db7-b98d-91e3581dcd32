<?php
declare(strict_types = 1);



$data               = [];
$comments           = '';
$service_id         = 0;
$service_name       ='';
$comments_id        = 0;
$id                 = 0;
$s_id               = 0;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {   
      $service_name = $_POST['service_name'];
      $s_id = $_POST['service_id'];
      $id = $_POST['comments_id'];
      $comments = $_POST['comments'];
      $service_id = intval($s_id);
      $comments_id = intval($id);


    
    $cms->getRepair()->setComment($service_name, $service_id, $comments_id, $comments);
}


redirect("repair_request/" . $service_id, $data); // Redirect to correct URL