<?php

namespace App\Livewire\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Layout('components.layouts.auth')]
class Login extends Component
{
    #[Validate('required|string')]
    public string $admin_id = '';

    #[Validate('required|string')]
    public string $password = '';

    public string $result = '';

    public bool $remember = false;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->ensureIsNotRateLimited();

        // Try authenticating as regular User first
        if (Auth::attempt(['admin_id' => $this->admin_id, 'password' => $this->password], $this->remember)) {
            RateLimiter::clear($this->throttleKey());
            Session::regenerate();

            if(Auth::user()->role === 'admin'){
                $this->redirectIntended(default: route('dashboard', absolute: false), navigate: true);
            } elseif(Auth::user()->role === 'school_admin') {
                $this->redirectIntended(default: route('dashboard-school-admin', absolute: false), navigate: true);
            } elseif(Auth::user()->role === 'user') {
                $this->redirectIntended(default: route('dashboard_school_user', absolute: false), navigate: true);
            }
            return;
            
        }

        // Try authenticating as SchoolUser
        if (Auth::guard('school')->attempt(['user_id' => $this->admin_id, 'password' => $this->password], $this->remember)) {
            RateLimiter::clear($this->throttleKey());
            Session::regenerate();
            
            $this->redirectIntended(default: route('dashboard_school_user', absolute: false), navigate: true);
            return;
        }

        // If both authentication attempts fail
        RateLimiter::hit($this->throttleKey());
        throw ValidationException::withMessages([
            'admin_id' => __('auth.failed'),
        ]);
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'admin_id' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->admin_id).'|'.request()->ip());
    }
}
