<?php

namespace App\Livewire;

use App\Models\User;
use App\Imports\UsersImport;
use App\Exports\UsersTemplateExport;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Livewire\Attributes\Layout;
use Maatwebsite\Excel\Facades\Excel;

#[Layout('components.layouts.app')]
class ManageUser extends Component
{
    use WithPagination, WithFileUploads;

    public $search = '';
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $excelFile;

    // Form fields
    public $admin_id = '';
    public $password = '';
    public $password_confirmation = '';
    public $editingUserId = null;

    protected $paginationTheme = 'bootstrap';

    public function rules()
    {
        $rules = [
            'admin_id' => 'required|string|max:255',
        ];

        if ($this->editingUserId) {
            $rules['admin_id'] .= '|unique:users,admin_id,' . $this->editingUserId;
            // 편집 시에는 비밀번호가 비어있으면 검증하지 않음
            if (!empty($this->password)) {
                $rules['password'] = 'required|string|min:6';
                $rules['password_confirmation'] = 'required|same:password';
            }
        } else {
            $rules['admin_id'] .= '|unique:users,admin_id';
            $rules['password'] = 'required|string|min:6';
            $rules['password_confirmation'] = 'required|same:password';
        }

        return $rules;
    }

    public function mount()
    {
        // 학교 관리자만 접근 가능하도록 체크
        if (auth()->user()->role !== 'school_admin') {
            abort(403, '접근 권한이 없습니다.');
        }
    }

    public function render()
    {
        $users = User::where('role', 'user')
            ->where('school_code', auth()->user()->school_code)
            ->when($this->search, function ($query) {
                $query->where('admin_id', 'like', '%' . $this->search . '%');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.manage-user', compact('users'));
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->resetForm();
    }

    public function openEditModal($userId)
    {
        $user = User::findOrFail($userId);

        // 같은 학교의 유저만 편집 가능
        if ($user->school_code !== auth()->user()->school_code) {
            session()->flash('error', '권한이 없습니다.');
            return;
        }

        $this->editingUserId = $user->id;
        $this->admin_id = $user->admin_id;
        $this->password = '';
        $this->password_confirmation = '';
        $this->showEditModal = true;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->resetForm();
    }

    public function openDeleteModal($userId)
    {
        $user = User::findOrFail($userId);

        // 같은 학교의 유저만 삭제 가능
        if ($user->school_code !== auth()->user()->school_code) {
            session()->flash('error', '권한이 없습니다.');
            return;
        }

        $this->editingUserId = $userId;
        $this->showDeleteModal = true;
    }

    public function closeDeleteModal()
    {
        $this->showDeleteModal = false;
        $this->editingUserId = null;
    }

    public function createUser()
    {
        $this->validate();

        User::create([
            'admin_id' => $this->admin_id,
            'password' => Hash::make($this->password),
            'role' => 'user',
            'school_code' => auth()->user()->school_code,
            'school_nm' => auth()->user()->school_nm,
        ]);

        session()->flash('message', '사용자가 성공적으로 생성되었습니다.');
        $this->closeCreateModal();
    }

    public function updateUser()
    {
        $this->validate();

        $user = User::findOrFail($this->editingUserId);

        // 같은 학교의 유저만 수정 가능
        if ($user->school_code !== auth()->user()->school_code) {
            session()->flash('error', '권한이 없습니다.');
            return;
        }

        $updateData = [
            'admin_id' => $this->admin_id,
        ];

        if (!empty($this->password)) {
            $updateData['password'] = Hash::make($this->password);
        }

        $user->update($updateData);

        session()->flash('message', '사용자가 성공적으로 수정되었습니다.');
        $this->closeEditModal();
    }

    public function deleteUser()
    {
        $user = User::findOrFail($this->editingUserId);

        // 같은 학교의 유저만 삭제 가능
        if ($user->school_code !== auth()->user()->school_code) {
            session()->flash('error', '권한이 없습니다.');
            return;
        }

        $user->delete();

        session()->flash('message', '사용자가 성공적으로 삭제되었습니다.');
        $this->closeDeleteModal();
    }

    public function importUsers()
    {
        $this->validate([
            'excelFile' => 'required|mimes:xlsx,xls,csv'
        ]);

        try {
            $import = new UsersImport(auth()->user()->school_code, auth()->user()->school_nm);
            Excel::import($import, $this->excelFile->path());

            session()->flash('message', '사용자들이 성공적으로 가져와졌습니다.');
            $this->excelFile = null;
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errorMessages = [];

            foreach ($failures as $failure) {
                $errorMessages[] = "행 {$failure->row()}: " . implode(', ', $failure->errors());
            }

            session()->flash('error', '유효성 검사 실패: ' . implode(' | ', $errorMessages));
        } catch (\Exception $e) {
            session()->flash('error', '가져오기 실패: ' . $e->getMessage());
        }
    }

    public function downloadTemplate()
    {
        return Excel::download(new UsersTemplateExport, 'users_template.xlsx');
    }

    private function resetForm()
    {
        $this->admin_id = '';
        $this->password = '';
        $this->password_confirmation = '';
        $this->editingUserId = null;
        $this->resetValidation();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }
}