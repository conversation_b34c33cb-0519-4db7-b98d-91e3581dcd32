<?php

namespace App\Livewire;

use App\Models\SwService;
use App\Models\ManageService;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.app')]
class ServicePage extends Component
{
    public $serviceId;
    public $service;

    public function mount($serviceId)
    {
        $this->serviceId = $serviceId;

        // 서비스 정보 조회
        $this->service = SwService::findOrFail($serviceId);

        // 현재 사용자가 이 서비스에 접근할 수 있는지 확인
        if (auth()->user()->role === 'user') {
            $hasAccess = ManageService::where('sw_service_id', $serviceId)
                ->where('school_code', auth()->user()->school_code)
                ->where('is_active', true)
                ->exists();

            if (!$hasAccess) {
                abort(403, '이 서비스에 접근할 권한이 없습니다.');
            }
        }

        // IT 기기 수리 요청 게시판인 경우 RepairBoard로 리다이렉트
        if ($serviceId == 2 || $serviceId == 16) { // ID 2와 16이 모두 IT 기기 수리 요청 게시판
            return redirect()->route('repair.board');
        }
    }

    public function render()
    {
        return view('livewire.service-page');
    }
}
