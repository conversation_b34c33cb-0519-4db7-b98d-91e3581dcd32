<?php

namespace App\Livewire;

use App\Models\RepairRequest;
use App\Models\RepairComment;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\WithPagination;

#[Layout('components.layouts.app')]
class RepairBoard extends Component
{
    use WithPagination;

    public $activeTab = 'request';

    // 요청하기 폼 필드
    public $grade = '';
    public $class = '';
    public $request = '';

    // 댓글 모달
    public $showCommentModal = false;
    public $selectedRequestId = null;
    public $newComment = '';
    public $requestComments = [];

    public function mount()
    {
        // 일반 사용자만 접근 가능
        if (auth()->user()->role !== 'user') {
            abort(403, '일반 사용자만 접근할 수 있습니다.');
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function submitRequest()
    {
        $this->validate([
            'grade' => 'required|string|max:50',
            'class' => 'required|string|max:50',
            'request' => 'required|string|max:1000',
        ], [
            'grade.required' => '학년을 입력해주세요.',
            'class.required' => '반을 입력해주세요.',
            'request.required' => '요구사항을 입력해주세요.',
        ]);

        RepairRequest::create([
            'school_code' => auth()->user()->school_code,
            'school_nm' => auth()->user()->school_nm,
            'user_id' => auth()->id(),
            'grade' => $this->grade,
            'class' => $this->class,
            'request' => $this->request,
            'status' => '요청',
        ]);

        $this->reset(['grade', 'class', 'request']);
        session()->flash('message', '수리 요청이 성공적으로 등록되었습니다.');

        // 요청리스트 탭으로 이동
        $this->activeTab = 'list';
    }

    public function openCommentModal($requestId)
    {
        $this->selectedRequestId = $requestId;
        $this->loadComments();
        $this->showCommentModal = true;
    }

    public function closeCommentModal()
    {
        $this->showCommentModal = false;
        $this->selectedRequestId = null;
        $this->newComment = '';
        $this->requestComments = [];
    }

    public function loadComments()
    {
        if ($this->selectedRequestId) {
            $this->requestComments = RepairComment::where('repair_request_id', $this->selectedRequestId)
                ->with('user')
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();
        }
    }

    public function addComment()
    {
        $this->validate([
            'newComment' => 'required|string|max:500',
        ], [
            'newComment.required' => '댓글을 입력해주세요.',
        ]);

        RepairComment::create([
            'repair_request_id' => $this->selectedRequestId,
            'user_id' => auth()->id(),
            'comment' => $this->newComment,
        ]);

        $this->newComment = '';
        $this->loadComments();
        session()->flash('comment_message', '댓글이 추가되었습니다.');
    }

    public function markAsComplete($requestId)
    {
        $request = RepairRequest::where('id', $requestId)
            ->where('school_code', auth()->user()->school_code)
            ->first();

        if ($request) {
            $request->update(['status' => '완료']);
            session()->flash('message', '요청이 완료 처리되었습니다.');
        }
    }

    public function render()
    {
        $pendingRequests = RepairRequest::forSchool(auth()->user()->school_code)
            ->byStatus('요청')
            ->with(['user', 'comments'])
            ->orderBy('created_at', 'desc')
            ->paginate(10, ['*'], 'pending');

        $completedRequests = RepairRequest::forSchool(auth()->user()->school_code)
            ->byStatus('완료')
            ->with('user')
            ->orderBy('updated_at', 'desc')
            ->paginate(10, ['*'], 'completed');

        return view('livewire.repair-board', compact('pendingRequests', 'completedRequests'));
    }
}
