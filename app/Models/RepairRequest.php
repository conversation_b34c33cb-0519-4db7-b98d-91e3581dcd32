<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairRequest extends Model
{
    protected $fillable = [
        'school_code',
        'school_nm',
        'user_id',
        'grade',
        'class',
        'request',
        'status',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(RepairComment::class);
    }

    public function scopeForSchool($query, $schoolCode)
    {
        return $query->where('school_code', $schoolCode);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
