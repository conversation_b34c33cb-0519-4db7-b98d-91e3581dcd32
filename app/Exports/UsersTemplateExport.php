<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UsersTemplateExport implements FromArray, WithHeadings, WithStyles
{
    public function array(): array
    {
        return [
            ['사용자ID는 영문/숫자 조합으로 입력하세요', '비밀번호는 6자 이상 입력하세요'],
            ['user001', 'password123'],
            ['user002', 'password456'],
            ['user003', 'password789'],
            ['teacher001', 'teacher123'],
            ['student001', 'student123'],
        ];
    }

    public function headings(): array
    {
        return [
            'admin_id',
            'password',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
            // Style the second row as description
            2 => [
                'font' => ['italic' => true, 'color' => ['rgb' => '666666']],
                'fill' => ['fillType' => 'solid', 'color' => ['rgb' => 'F0F0F0']]
            ],
        ];
    }
}
