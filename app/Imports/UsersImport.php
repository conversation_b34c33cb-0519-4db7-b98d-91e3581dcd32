<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithStartRow;

class UsersImport implements ToModel, WithHeadingRow, WithValidation, WithStartRow
{
    use Importable;

    protected $schoolCode;
    protected $schoolNm;

    public function __construct($schoolCode, $schoolNm)
    {
        $this->schoolCode = $schoolCode;
        $this->schoolNm = $schoolNm;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new User([
            'admin_id' => $row['admin_id'],
            'password' => Hash::make($row['password']),
            'role' => 'user',
            'school_code' => $this->schoolCode,
            'school_nm' => $this->schoolNm,
        ]);
    }

    public function rules(): array
    {
        return [
            'admin_id' => 'required|string|unique:users,admin_id',
            'password' => 'required|string|min:6',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'admin_id.required' => '아이디는 필수입니다.',
            'admin_id.unique' => '이미 존재하는 아이디입니다.',
            'password.required' => '비밀번호는 필수입니다.',
            'password.min' => '비밀번호는 최소 6자 이상이어야 합니다.',
        ];
    }

    public function startRow(): int
    {
        return 3; // 헤더(1행)와 설명(2행)을 건너뛰고 3행부터 시작
    }
}
